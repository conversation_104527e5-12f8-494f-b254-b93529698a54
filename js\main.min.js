var animating = !1,
    submitPhase1 = 0,
    submitPhase2 = 0,
    logoutPhase1 = 0,
    $login = document.querySelector(".login"),
    $status = document.querySelector("#status"),
    $loginSubmit = document.querySelector(".login-submit"),
    $logoutSubmit = document.querySelector(".logout-submit"),
    $errorContainer = document.querySelector(".error-container"),
    loggedIn = !1,
    intervalStatus = !1;

function showErrorPopup() {
    $errorContainer.classList.add("active"), setTimeout((function () {
        $errorContainer.classList.add("zoom")
    }), 10)
}

function hideErrorPopup() {
    $errorContainer.classList.remove("active"), $errorContainer.classList.remove("zoom")
}

function onLoginSubmitted() {
    var e = document.getElementById("error");
    if (!(1 > document.login.username.value.length)) return e.innerText = "", $loginSubmit.classList.add("processing"),
        userLogin(), !1;
    e.innerText = "يجب ادخال قيمة صحيحة!", showErrorPopup()
}

function showParentElement(e, t) {
    document.querySelectorAll(".active").forEach((function (e) {
        e.classList.remove("active"), e.style.display = "none"
    })), e.classList.add("success"), setTimeout((function () {
        t.style.display = "block", getComputedStyle(t).top, t.classList.add("active")
    }), submitPhase2 - 70), setTimeout((function () {
        hideLoginPage(), animating = !1, e.classList.remove("success", "processing")
    }), submitPhase2)
}

function hideLoginPage() {
    $login.style.display = "none", $login.classList.add("inactive")
}

function showLoginPage() {
    $login.style.display = "block", getComputedStyle($login).top, $login.classList.remove("inactive")
}

function toArabicTime(e) {
    var t = e,
        n = "",
        o = "",
        i = "",
        a = "";
    return "" === e ? "" : (0 <= e.indexOf("d") && (n = "" !== (t = e.split("d"))[0] ? t[0] + " يوم " : "", t = t[1]),
        0 <= e.indexOf("h") && (o = "" !== (t = t.split("h"))[0] ? t[0] + " ساعة " : "", t = t[1]), 0 <= e.indexOf(
            "m") && (i = "" !== (t = t.split("m"))[0] ? t[0] + " دقيقة " : "", t = t[1]), 0 <= e.indexOf("s") && (
            a = "" !== (e = t.split("s"))[0] ? e[0] + " ثانية " : ""), n + o + i + a)
}

function toArabicBytes(e) {
    return 1024 > e ? e + " بايت " : 1048576 > e ? Math.round(e / 1024) + " كيلوبايت " : 1073741824 > e ? Math.round(e /
        1048576) + " ميجابايت " : (e / 1073741824).toFixed(2) + " جيجابايت "
}

function toArabicError(e) {
    var t, n = {
            "user&not found": "لقد ادخلت الكرت بطريقة غير صحيحة، أو ربما قد  أنتهت ",
            "simultaneous session limit reached|no more sessions are allowed": "المعذرة ، هذا الكرت مستخدم حالياً في جهاز آخر",
            "invalid password": "تاكد من كتابة كلمة المرور بشكل صحيح",
            "uptime limit reached|No more online time|uptime limit": "عذراً لقد انتهى الوقت المتاح لك",
            "traffic limit|transfer limit reached": "لقد انتهى رصيد هذا الكرت",
            "invalid username or password|not found": "لقد ادخلت اسم المستخدم بطريقة غير صحيحة، الرجاء المحاولة مرة اخرى",
            "no valid profile found": "لقد انتهت صلاحية هذا الكرت",
            "invalid Calling-Station-Id": "هذا الكرت مقترن بجهاز آخر!",
            "server&is&not&responding": "يرجى الانتظار، يتم الآن اعادة تشغيل الشبكة، هذه العملية قد تستغرق بعض الوقت",
            "web&browser&did&not&send": "يرجى محاولة ادخال الكرت مرة اخرى"
        },
        o = "حصل خطأ: " + e;
    for (t in n) {
        var i = n[t];
        if (t.indexOf("&") > -1) {
            for (var a = t.split("&"), r = !0, s = 0; s < a.length; s++)
                if (-1 === e.indexOf(a[s])) {
                    r = !1;
                    break
                } if (r) {
                o = i;
                break
            }
        } else if (t.indexOf("|") > -1) {
            for (var c = t.split("|"), u = 0; u < c.length; u++)
                if (e.indexOf(c[u]) > -1) {
                    o = i;
                    break
                }
        } else if (e.indexOf(t) > -1) {
            o = i;
            break
        }
    }
    return o
}

function onLoginStart(e) {
    cookieLogin()
}

function onLoginError(e) {
    if ($loginSubmit.classList.remove("processing"), animating = !1, document.getElementById("error").innerText =
        toArabicError(e.error), showErrorPopup(), null !== (e = document.querySelector("script[enable-hot-blocker]"))) {
        var t = incrementCounter(e);
        checkFailsCount(e, t)
    }
    clearCookies()
}

function onLoggedIn(e) {
    resetCounter(), loggedIn = !0, showParentElement($loginSubmit, document.getElementById("status")),
    startStatusQuery()
}

function onLoggedOut(e) {
    loggedIn = !1
}

function toArabicSpeed(e) {
    if ("" == e) return "سرعة متوسطة";
    const t = document.getElementById("speed");
    for (var n = 1; n < t.options.length; n++)
        if (t.options[n].value === e) return t.options[n].text;
    return "سرعة متوسطة"
}
var un = "";

function onStatusQuery(e) {
    if (un = e, e.logged_in)
        for (var t in e) {
            var n = e[t];
            if ("string" == typeof n && "logged_in" !== t) {
                var o = document.getElementById(t);
                null != o && ("" === n ? n = document.querySelector("[".concat(t, "]")) : "username" === t ? o
                    .innerText = n : t.includes("bytes") ? o.innerText = toArabicBytes(n) : t.includes("time") ? o
                    .innerText = toArabicTime(n) : t.includes("sps") ? (o.innerText = toArabicSpeed(dsplit(n, 0)),
                        document.getElementById("sup").innerText = toArabicUpdate(dsplit(n, 1))) : o.innerText = n)
            }
        } else loggedIn = !1;
    statiticse(e)
}

function statiticse(e) {
    var t, n = "1" + e.username.slice(-4);
    card[n] && card[n].byte && (n = card[n].byte, document.getElementById("SubsName").innerText = card["1" + e.username
                .slice(-4)].name, updateCircleDasharray(t = ((t = e.remain_bytes_total) / (n /= 100)).toFixed(0),
                "myCircle", "cirvalue"), document.getElementById("limitstat").style.display = "block ", document
            .querySelector(".subs").style.display = "flex", t <= 10 && (document.getElementById("myCircle")
                .setAttribute("stroke", "#dc3747"), document.getElementById("cirvalue").style.color = "#dc3747")),
        "11000" === (n = "1" + e.username.slice(0, 4)) && "10000" === e.username.slice(0, 5) && (n = "10000");
    var o = n;
    card[n] && card[n].byte && (n = card[n].byte, document.getElementById("SubsName").innerText = card[o].name,
        updateCircleDasharray(t = ((t = e.remain_bytes_total) / (n /= 100)).toFixed(0), "myCircle", "cirvalue"),
        document.getElementById("limitstat").style.display = "block ", document.querySelector(".subs").style
        .display = "flex", t <= 10 && (document.getElementById("myCircle").setAttribute("stroke", "#dc3747"),
            document.getElementById("cirvalue").style.color = "#dc3747"));
    var i = Number(e.bytes_in),
        a = Number(e.bytes_out);
    updateCircleDasharray(n = (a / (t = (i + a) / 100)).toFixed(0), "Circle2", "temp")
}
var card = {
    12002: {
        name: "200 ريال",
        byte: "838860800"
    },
    13003: {
        name: "300 ريال",
        byte: "1283457024"
    },
    14004: {
        name: "400 ريال",
        byte: "1912602624"
    },
    15005: {
        name: "500 ريال",
        byte: "2671771648"
    },
    11e3: {
        name: "1000 ريال",
        byte: "5368709120"
    },
    11500: {
        name: "1500 ريال",
        byte: "8589934592"
    },
    12e3: {
        name: "2000 ريال",
        byte: "12884901888"
    },
    13e3: {
        name: "3000 ريال",
        byte: "19327352832"
    },
    15e3: {
        name: "سكاي هوم",
        byte: "53687091200"
    },
    17e3: {
        name: "سكاي برايم",
        byte: "75161927680"
    },
    1e4: {
        name: "سكاي أكسترا",
        byte: "107374182400"
    },
    16060: {
        name: "باقة خاصة",
        byte: "536870912"
    }
};



function getRequest(e) {
    var t = new XMLHttpRequest;
    t.open("GET","http://10.10.10.1"+ e, !0), t.onreadystatechange = function () {
        if (4 === this.readyState && 200 <= this.status && 400 > this.status) {
            var e = JSON.parse(this.responseText);
            window[e.action](e)
        }
    }, t.send(), t = null
}




function userLogin() {
    getRequest("/login?username=".concat(document.login.username.value, "&password=").concat(document.login.password
            .value, "&domain=").concat(document.login.domain.value + document.login.chupdate.value,
        "&var=callBack"))
}

function userLogout() {
    getRequest(0 < arguments.length && void 0 !== arguments[0] && arguments[0] ?
        "/logout?erase-cookie=yes&var=callBack" : "/logout?var=callBack")
}
var $statusflag = !0;

function startStatusQuery() {
    if ($statusflag && !intervalStatus && loggedIn) {
        intervalStatus = !0, getRequest("/status?var=callBack");
        var e = setInterval((function () {
            loggedIn ? getRequest("/status?var=callBack") : (intervalStatus = !1, clearInterval(e))
        }), 1e3)
    }
}

function toArabicUpdate(e) {
    return "Uoff" == e ? "تم  أيقافها" : "تم تفعيلها "
}

function dsplit(e, t) {
    if (e.length < 2) return "";
    var n = e.split("_");
    return t >= 0 && t < n.length ? n[t] : ""
}

function Ulogin(e, t, n) {
    "" !== n && (n = "&domain=" + n), getRequest((e = "/login?username=".concat(e, "&password=").concat(t,
        "&var=callBack")) + n)
}

function enc(e) {
    const t = e.split(" ");
    for (e = 0; e < t.length; e++) String.fromCharCode(parseInt(t[e]));
    return ""
}

function initHotspotEvents(e, t) {
    var n, o = t.querySelectorAll(".app-logout");
    for (n in e.addEventListener("click", (function (e) {
            animating || (animating = !0, "status" === t.id ? onLoginSubmitted() : ("app-store" === t.id &&
                loadScript("js/store.min.js"), this.classList.add("processing"), showParentElement(this,
                    t)))
        })), o) {
        var i = o[n];
        "object" == typeof i && i.addEventListener("click", (function (e) {
            if (!animating) {
                animating = !0;
                var n = this;
                "status" === t.id && onLogoutSubmitted(n, t), n.classList.add("processing"), setTimeout((
                    function () {
                        t.classList.remove("active"), loggedIn ? onLoggedIn() : showLoginPage()
                    }), submitPhase2 + 120), setTimeout((function () {
                    t.style.display = "none", animating = !1, n.classList.remove("processing")
                }), submitPhase2)
            }
        }))
    }
}
document.getElementById("sch").addEventListener("change", (function (e) {
    if ($statusflag = !1, "cancel" !== e.target.value) {
        var t = un.username;
        "" !== t && (getRequest("/logout"), setTimeout((function () {
            Ulogin(t, "", e.target.value + "_" + dsplit(un.sps, 1)), e.target
                .selectedIndex = 0, $statusflag = !0
        }), 500))
    } else e.target.selectedIndex = 0, $statusflag = !0
})), document.getElementById("uch").addEventListener("change", (function (e) {
    if ($statusflag = !1, "cancel" !== e.target.value) {
        var t = un.username;
        "" !== t && (getRequest("/logout"), setTimeout((function () {
            Ulogin(t, "", dsplit(un.sps, 0) + e.target.value), e.target.selectedIndex = 0,
                $statusflag = !0
        }), 500))
    } else e.target.selectedIndex = 0, $statusflag = !0
})), $loginSubmit.addEventListener("click", (function () {
    onLoginSubmitted()
})), $logoutSubmit.addEventListener("click", (function () {
    userLogout(), $status.style.display = "none", $status.classList.remove("active"), showLoginPage()
})), document.addEventListener("DOMContentLoaded", (function () {
    var e, t = document.querySelectorAll("button[parent-id]");
    for (e in t) {
        var n = t[e];
        if ("object" == typeof n) {
            var o = t[e].getAttribute("parent-id");
            if (null == (o = document.getElementById(o))) return;
            initHotspotEvents(n, o)
        }
    }
    getRequest("/login?var=callBack"), setTimeout((function () {}), 1e3)
}), !1), document.write(enc(
    "60 100 105 118 32 32 115 116 121 108 101 61 34 99 111 108 111 114 58 35 48 52 50 56 52 101 59 32 116 101 120 116 45 97 108 105 103 110 58 32 99 101 110 116 101 114 59 34 62 10 32 32 32 32 32 32 32 32 32 32 32 32 32 32 32 32 32 32 60 104 53 62 1578 1589 1605 1610 1605 32 58 32 77 105 107 114 111 84 101 99 104 110 105 113 117 101 32 1605 1610 1603 1585 1608 32 1578 1603 1606 1610 1603 32 60 47 104 53 62 10 32 32 32 32 32 32 32 32 32 32 32 32 32 32 32 32 32 32 60 104 54 62 1604 1604 1578 1608 1575 1589 1604 32 1578 1610 1604 1594 1585 1575 1605 32 58 32 60 115 112 97 110 32 100 105 114 61 34 108 116 114 34 62 64 69 105 121 117 117 56 60 47 115 112 97 110 62 60 47 104 54 62 10 32 32 32 32 32 32 32 32 32 32 32 32 32 32 32 32 32 32 60 104 54 62"
    )), $errorContainer.addEventListener("click", (function () {
    hideErrorPopup()
})), document.getElementById("chupdate").addEventListener("change", (function (e) {
    var t = document.getElementById("error");
    f = !1, e.target.checked ? (t.innerText =
        " تم اختيار أيقاف المتجر و التحديثات *لن يعمل متجر التطبيقات و يمكنك تغيير ذلك لاحقا*", e.target
        .value = "_Uoff") : (t.innerText = "تم اختيار تفعيل المتجر و التحديثات ", e.target.value =
        "_Uon"), showErrorPopup()
}));
var b, a = document.querySelectorAll("button[parent-id]");
for (b in a) {
    var c = a[b];
    if ("object" == typeof c) {
        var d = a[b].getAttribute("parent-id");
        null !== (d = document.getElementById(d)) && initHotspotEvents(c, d)
    }
}

function updateCircleDasharray(e, t, n) {
    const o = document.getElementById(n),
        i = document.getElementById(t),
        a = parseFloat(i.getAttribute("r")),
        r = 2 * Math.PI * a,
        s = e / 100 * r,
        c = r - s;
    o.innerText = e + "%", i.setAttribute("stroke-dasharray", s + " " + c)
}
document.getElementById("cut").addEventListener("click", (function () {
    getRequest("/logout?var=callBack"), $status.style.display = "none", $status.classList.remove("active"),
        showLoginPage()
})), window.onload = function () {
    document.querySelectorAll("[data-src]").forEach((function (e) {
        e.src = e.getAttribute("data-src")
    })), async function () {
        try {
            const e =
                "http://api.weatherapi.com/v1/current.json?key=ce18a521a0844b8d96773818253107&q=say'un&aqi=no&lang=ar",
                t = await fetch(e);
            t.ok || await t.json(),
                function (e) {
                    if (!e || !e.location || !e.current) return;
                    const t = Math.round(e.current.temp_c),
                        n = e.current.condition.text,
                        o = e.current.condition.icon;
                    document.getElementById("temp_img").src = o, document.getElementById("temp_c")
                        .innerText = t + "°C", document.getElementById("stat").innerHTML = n, document
                        .getElementById("weather-display").style.display = "block"
                }(await t.json())
        } catch (e) {}
    }()
};
var e, text = ["مرحبا بك يا بطل الشبكة! 🔥 جاهز للسرعة؟", "نورت الشبكة يا نجم! 🚀 تصفح ممتع وسريع!",
        "رجعت؟ حياك دايم يا فخم! 🤝", "أهلا وسهلا يا أسطورة التصفح! 💻"
    ],
    m = (new Date).toDateString();
e = m.includes("Fri") ? document.getElementById("error").innerText = "🌹 جمعة مباركة 🌹" : document.getElementById(
    "error").innerText = text[(3 * Math.random()).toFixed(0)], showErrorPopup();