import 'package:flutter/material.dart';
import '../utils/app_colors.dart';
import '../screens/prices_screen.dart';
import '../screens/packages_screen.dart';
import '../screens/locations_screen.dart';
import '../screens/status_screen.dart';
import '../screens/info_screen.dart';

class HomeScreen extends StatefulWidget {
  final String initialTab;
  final String? cardNumber;
  final String? selectedSpeed;

  const HomeScreen({
    super.key,
    this.initialTab = 'status',
    this.cardNumber,
    this.selectedSpeed,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late String currentTab;

  @override
  void initState() {
    super.initState();
    currentTab = widget.initialTab;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: _buildCurrentScreen(),
      ),
    );
  }

  Widget _buildCurrentScreen() {
    switch (currentTab) {
      case 'prices':
        return PricesScreen(onBack: () => _navigateBack());
      case 'packages':
        return PackagesScreen(onBack: () => _navigateBack());
      case 'locations':
        return LocationsScreen(onBack: () => _navigateBack());
      case 'info':
        return InfoScreen(onBack: () => _navigateBack());
      case 'status':
      default:
        return StatusScreen(
          cardNumber: widget.cardNumber ?? '',
          selectedSpeed: widget.selectedSpeed ?? 'normal',
          onNavigate: (tab) => _navigateTo(tab),
          onLogout: () => _logout(),
        );
    }
  }

  void _navigateTo(String tab) {
    setState(() {
      currentTab = tab;
    });
  }

  void _navigateBack() {
    setState(() {
      currentTab = 'status';
    });
  }

  void _logout() {
    Navigator.of(context).popUntil((route) => route.isFirst);
  }
}
