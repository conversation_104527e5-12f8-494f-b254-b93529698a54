import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../utils/app_colors.dart';
import '../widgets/custom_button.dart';
import '../widgets/custom_dropdown.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/weather_widget.dart';
import '../widgets/logo_widget.dart';
import '../services/hotspot_service.dart';
import '../utils/error_messages.dart';
import 'home_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController _cardNumberController = TextEditingController();
  String? _selectedSpeed;
  bool _blockGoogleUpdates = false;
  bool _isLoading = false;

  final List<Map<String, String>> _speedOptions = [
    {'value': 'economic', 'label': 'سرعة اقتصادية'},
    {'value': 'normal', 'label': 'سرعة متوسطة'},
    {'value': 'high', 'label': 'سرعة عالية'},
    {'value': 'very', 'label': 'سرعة مفتوحة'},
    {'value': 'gaming', 'label': 'سرعة ألعاب أون لاين'},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            padding: const EdgeInsets.all(15),
            child: Column(
              children: [
                // Main container with rounded corners and shadow
                Container(
                  width: double.infinity,
                  constraints: const BoxConstraints(maxWidth: 500),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(70),
                    boxShadow: const [
                      BoxShadow(
                        color: AppColors.shadowDark,
                        offset: Offset(5, 5),
                        blurRadius: 10,
                      ),
                      BoxShadow(
                        color: AppColors.shadowLight,
                        offset: Offset(-5, -5),
                        blurRadius: 10,
                      ),
                    ],
                    color: AppColors.background,
                  ),
                  child: Column(
                    children: [
                      // Top bar
                      Container(
                        height: 10,
                        width: 150,
                        margin: const EdgeInsets.only(top: 20),
                        decoration: BoxDecoration(
                          color: AppColors.weatherBg,
                          borderRadius: BorderRadius.circular(5),
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Header with logo
                      const LogoWidget(),

                      const SizedBox(height: 20),

                      // Weather widget
                      const WeatherWidget(),

                      const SizedBox(height: 20),

                      // Contact buttons
                      _buildContactButtons(),

                      const SizedBox(height: 20),

                      // Login form
                      _buildLoginForm(),

                      const SizedBox(height: 20),

                      // Bottom buttons
                      _buildBottomButtons(),

                      const SizedBox(height: 20),

                      // Footer
                      const Text(
                        'برمجة و تصميم : أبو أمير رائد فراره',
                        style: TextStyle(
                          color: AppColors.textLight,
                          fontSize: 12,
                        ),
                      ),

                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContactButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        CustomButton(
          icon: 'assets/icons/c.svg',
          label: 'اتصل بنا',
          onPressed: () => _makePhoneCall('+967770160624'),
        ),
        CustomButton(
          icon: 'assets/icons/w.svg',
          label: 'واتساب',
          onPressed: () => _openWhatsApp('+967770160624'),
        ),
        CustomButton(
          icon: 'assets/icons/i.svg',
          label: 'معلومات',
          onPressed: () => _showInfoDialog(),
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 10),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(30),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowDark,
            offset: Offset(5, 5),
            blurRadius: 5,
          ),
          BoxShadow(
            color: AppColors.shadowLight,
            offset: Offset(-5, -5),
            blurRadius: 5,
          ),
        ],
        color: AppColors.background,
      ),
      child: Column(
        children: [
          // Speed selection dropdown
          CustomDropdown(
            hint: 'أختيار سرعة الإنترنت',
            value: _selectedSpeed,
            items: _speedOptions,
            onChanged: (value) {
              setState(() {
                _selectedSpeed = value;
              });
            },
          ),

          const SizedBox(height: 15),

          // Card number input
          CustomTextField(
            controller: _cardNumberController,
            hint: 'أدخل رقم الكرت هنا',
            keyboardType: TextInputType.number,
          ),

          const SizedBox(height: 20),

          // Login button
          SizedBox(
            width: 200,
            height: 50,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _login,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                elevation: 5,
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Text(
                      'دخول الإنترنت',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),

          const SizedBox(height: 20),

          // Google updates toggle
          _buildGoogleUpdatesToggle(),
        ],
      ),
    );
  }

  Widget _buildGoogleUpdatesToggle() {
    return Container(
      padding: const EdgeInsets.all(10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            'ايقاف تحديثات جوجل',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Switch(
            value: _blockGoogleUpdates,
            onChanged: (value) {
              setState(() {
                _blockGoogleUpdates = value;
              });
            },
            activeColor: AppColors.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        CustomButton(
          icon: 'assets/icons/d.svg',
          label: 'أسعار الكروت',
          onPressed: () => _navigateToScreen('prices'),
        ),
        CustomButton(
          icon: 'assets/icons/st.svg',
          label: 'QR CODE',
          onPressed: () => _openQRCode(),
        ),
        CustomButton(
          icon: 'assets/icons/l.svg',
          label: 'أماكن البيع',
          onPressed: () => _navigateToScreen('locations'),
        ),
      ],
    );
  }

  void _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);
    await launchUrl(launchUri);
  }

  void _openWhatsApp(String phoneNumber) async {
    final Uri launchUri = Uri.parse('https://wa.me/$phoneNumber');
    await launchUrl(launchUri);
  }

  void _openQRCode() async {
    // This would open the QR code scanner or redirect to QR login
    const String qrUrl = 'https://connect4ar.com/qr/login';
    final Uri launchUri = Uri.parse(qrUrl);
    await launchUrl(launchUri);
  }

  void _showInfoDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('عن سكاي نت'),
        content: const Text(
          'نحن نسعى لتقديم أفضل خدمة إنترنت لعملائنا الكرام.\n\n'
          'بجودة عالية وأسعار تنافسية.\n\n'
          'استمتع بتصفح سريع، ومشاهدة ممتعة، وتواصل غير محدود.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _navigateToScreen(String screenType) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HomeScreen(initialTab: screenType),
      ),
    );
  }

  void _login() async {
    if (_cardNumberController.text.isEmpty || _selectedSpeed == null) {
      _showErrorMessage('يرجى إدخال رقم الكرت واختيار السرعة');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Prepare update settings
      String updateSettings = _blockGoogleUpdates ? '_Uoff' : '_Uon';

      // Attempt login
      final result = await HotspotService.instance.login(
        username: _cardNumberController.text,
        domain: _selectedSpeed! + updateSettings,
      );

      if (result.isSuccess) {
        if (mounted) {
          // Show welcome message
          _showWelcomeMessage();

          // Navigate to home screen with status
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => HomeScreen(
                initialTab: 'status',
                cardNumber: _cardNumberController.text,
                selectedSpeed: _selectedSpeed!,
              ),
            ),
          );
        }
      } else {
        if (mounted) {
          _showErrorMessage(result.error ?? 'فشل في تسجيل الدخول');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorMessage('خطأ في الشبكة: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showWelcomeMessage() {
    final message = WelcomeMessages.getRandomMessage();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  void dispose() {
    _cardNumberController.dispose();
    super.dispose();
  }
}
