import 'package:flutter/material.dart';
import '../utils/app_colors.dart';

class CustomButton extends StatelessWidget {
  final String icon;
  final String label;
  final VoidCallback onPressed;
  final double? width;
  final double? height;

  const CustomButton({
    super.key,
    required this.icon,
    required this.label,
    required this.onPressed,
    this.width = 90,
    this.height = 90,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: Colors.black, width: 1),
          boxShadow: const [
            BoxShadow(
              color: Color(0xFFc6c6c6),
              offset: Offset(5, 5),
              blurRadius: 8,
            ),
            BoxShadow(
              color: AppColors.shadowLight,
              offset: Offset(-5, -5),
              blurRadius: 5,
            ),
          ],
          color: AppColors.background,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon container
            Container(
              width: 35,
              height: 35,
              margin: const EdgeInsets.only(bottom: 5),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                color: AppColors.background,
              ),
              child: _buildIcon(),
            ),
            
            // Label
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w700,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIcon() {
    // Map icon names to actual icons
    IconData iconData;
    Color iconColor = AppColors.textPrimary;
    
    switch (icon) {
      case 'assets/icons/c.svg':
        iconData = Icons.phone;
        iconColor = AppColors.primary;
        break;
      case 'assets/icons/w.svg':
        iconData = Icons.chat;
        iconColor = AppColors.whatsapp;
        break;
      case 'assets/icons/i.svg':
        iconData = Icons.info;
        iconColor = AppColors.primary;
        break;
      case 'assets/icons/d.svg':
        iconData = Icons.price_check;
        iconColor = AppColors.primary;
        break;
      case 'assets/icons/st.svg':
        iconData = Icons.qr_code;
        iconColor = AppColors.primary;
        break;
      case 'assets/icons/l.svg':
        iconData = Icons.location_on;
        iconColor = AppColors.primary;
        break;
      default:
        iconData = Icons.help;
        iconColor = AppColors.primary;
    }
    
    return Icon(
      iconData,
      size: 25,
      color: iconColor,
    );
  }
}
