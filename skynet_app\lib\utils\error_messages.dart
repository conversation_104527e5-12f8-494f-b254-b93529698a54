class ErrorMessages {
  static final Map<String, String> _errorMap = {
    'user&not found': 'لقد ادخلت الكرت بطريقة غير صحيحة، أو ربما قد انتهت',
    'simultaneous session limit reached|no more sessions are allowed': 'المعذرة، هذا الكرت مستخدم حالياً في جهاز آخر',
    'invalid password': 'تاكد من كتابة كلمة المرور بشكل صحيح',
    'uptime limit reached|No more online time|uptime limit': 'عذراً لقد انتهى الوقت المتاح لك',
    'traffic limit|transfer limit reached': 'لقد انتهى رصيد هذا الكرت',
    'invalid username or password|not found': 'لقد ادخلت اسم المستخدم بطريقة غير صحيحة، الرجاء المحاولة مرة اخرى',
    'no valid profile found': 'لقد انتهت صلاحية هذا الكرت',
    'invalid Calling-Station-Id': 'هذا الكرت مقترن بجهاز آخر!',
    'server&is&not&responding': 'يرجى الانتظار، يتم الآن اعادة تشغيل الشبكة، هذه العملية قد تستغرق بعض الوقت',
    'web&browser&did&not&send': 'يرجى محاولة ادخال الكرت مرة اخرى',
  };

  static String toArabic(String error) {
    String arabicError = 'حصل خطأ: $error';

    for (String key in _errorMap.keys) {
      String arabicMessage = _errorMap[key]!;

      if (key.contains('&')) {
        // Handle AND conditions
        List<String> conditions = key.split('&');
        bool allMatch = true;
        
        for (String condition in conditions) {
          if (!error.contains(condition)) {
            allMatch = false;
            break;
          }
        }
        
        if (allMatch) {
          arabicError = arabicMessage;
          break;
        }
      } else if (key.contains('|')) {
        // Handle OR conditions
        List<String> conditions = key.split('|');
        
        for (String condition in conditions) {
          if (error.contains(condition)) {
            arabicError = arabicMessage;
            break;
          }
        }
        
        if (arabicError == arabicMessage) break;
      } else {
        // Simple contains check
        if (error.contains(key)) {
          arabicError = arabicMessage;
          break;
        }
      }
    }

    return arabicError;
  }
}

class TimeFormatter {
  static String toArabicTime(String time) {
    if (time.isEmpty) return '';

    String result = '';
    String remaining = time;

    // Days
    if (time.contains('d')) {
      List<String> parts = remaining.split('d');
      if (parts[0].isNotEmpty) {
        result += '${parts[0]} يوم ';
      }
      remaining = parts[1];
    }

    // Hours
    if (time.contains('h')) {
      List<String> parts = remaining.split('h');
      if (parts[0].isNotEmpty) {
        result += '${parts[0]} ساعة ';
      }
      remaining = parts[1];
    }

    // Minutes
    if (time.contains('m')) {
      List<String> parts = remaining.split('m');
      if (parts[0].isNotEmpty) {
        result += '${parts[0]} دقيقة ';
      }
      remaining = parts[1];
    }

    // Seconds
    if (time.contains('s')) {
      List<String> parts = remaining.split('s');
      if (parts[0].isNotEmpty) {
        result += '${parts[0]} ثانية ';
      }
    }

    return result.trim();
  }
}

class SpeedFormatter {
  static String toArabicSpeed(String speed) {
    if (speed.isEmpty) return 'سرعة متوسطة';

    switch (speed) {
      case 'economic':
        return 'سرعة اقتصادية';
      case 'normal':
        return 'سرعة متوسطة';
      case 'high':
        return 'سرعة عالية';
      case 'very':
        return 'سرعة مفتوحة';
      case 'gaming':
        return 'سرعة ألعاب أون لاين';
      default:
        return 'سرعة متوسطة';
    }
  }

  static String toArabicUpdate(String update) {
    return update == 'Uoff' ? 'تم أيقافها' : 'تم تفعيلها';
  }
}

class WelcomeMessages {
  static final List<String> messages = [
    'مرحبا بك يا بطل الشبكة! 🔥 جاهز للسرعة؟',
    'نورت الشبكة يا نجم! 🚀 تصفح ممتع وسريع!',
    'رجعت؟ حياك دايم يا فخم! 🤝',
    'أهلا وسهلا يا أسطورة التصفح! 💻',
  ];

  static String getRandomMessage() {
    final now = DateTime.now();
    
    // Special message for Friday
    if (now.weekday == DateTime.friday) {
      return '🌹 جمعة مباركة 🌹';
    }
    
    // Random welcome message
    final index = (messages.length * (DateTime.now().millisecondsSinceEpoch % 1000) / 1000).floor();
    return messages[index.clamp(0, messages.length - 1)];
  }
}
