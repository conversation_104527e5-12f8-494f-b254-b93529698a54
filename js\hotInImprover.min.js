function bg(){document.querySelectorAll("input[improve-input]").forEach((function(e){e.addEventListener("input",(function(){var e=this.value;this.hasAttribute("rm-white-spaces")&&(e=e.split(" ").join("")),this.hasAttribute("to-lower")&&(e=e.toLowerCase()),this.hasAttribute("to-upper")&&(e=e.toUpperCase()),this.hasAttribute("to-arabic-numbers")&&(e=e.replace(RegExp(":","g"),"0").replace(new RegExp(String.fromCharCode(1632),"g"),"0").replace(new RegExp(String.fromCharCode(1633),"g"),"1").replace(new RegExp(String.fromCharCode(1634),"g"),"2").replace(new RegExp(String.fromCharCode(1635),"g"),"3").replace(new RegExp(String.fromCharCode(1636),"g"),"4").replace(new RegExp(String.fromCharCode(1637),"g"),"5").replace(new RegExp(String.fromCharCode(1638),"g"),"6").replace(new RegExp(String.fromCharCode(1639),"g"),"7").replace(new RegExp(String.fromCharCode(1640),"g"),"8").replace(new RegExp(String.fromCharCode(1641),"g"),"9")),this.hasAttribute("only-numbers")&&(e=e.replace(/\D/g,"")),this.hasAttribute("no-numbers")&&(e=e.replace(/[0-9]/g,"")),this.hasAttribute("only-alphanumeric")&&(e=e.replace(/[^0-9a-z]/gi,"")),this.value=e}))}))}document.addEventListener("DOMContentLoaded",(function(){bg()}),!1);

