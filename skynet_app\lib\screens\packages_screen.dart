import 'package:flutter/material.dart';
import '../utils/app_colors.dart';

class PackagesScreen extends StatelessWidget {
  final VoidCallback onBack;

  const PackagesScreen({super.key, required this.onBack});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: const EdgeInsets.all(15),
        child: Column(
          children: [
            // Main container
            Container(
              width: double.infinity,
              constraints: const BoxConstraints(maxWidth: 500),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(70),
                boxShadow: const [
                  BoxShadow(
                    color: AppColors.shadowDark,
                    offset: Offset(5, 5),
                    blurRadius: 10,
                  ),
                  BoxShadow(
                    color: AppColors.shadowLight,
                    offset: Offset(-5, -5),
                    blurRadius: 10,
                  ),
                ],
                color: AppColors.background,
              ),
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  
                  // Header
                  _buildHeader(),
                  
                  const SizedBox(height: 20),
                  
                  // Packages content
                  _buildPackagesContent(),
                  
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          // Text section
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const Text(
                  'تفاصيل !',
                  style: TextStyle(
                    fontSize: 30,
                    color: AppColors.textSecondary,
                  ),
                ),
                const Text(
                  'الباقات المنزلية',
                  style: TextStyle(
                    fontSize: 35,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 20),
          
          // Back button
          GestureDetector(
            onTap: onBack,
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                border: Border.all(color: Colors.black),
                boxShadow: const [
                  BoxShadow(
                    color: AppColors.shadowDark,
                    offset: Offset(5, 5),
                    blurRadius: 5,
                  ),
                  BoxShadow(
                    color: AppColors.shadowLight,
                    offset: Offset(-5, -5),
                    blurRadius: 5,
                  ),
                ],
                color: AppColors.background,
              ),
              child: const Icon(
                Icons.arrow_back,
                size: 30,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPackagesContent() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        children: [
          // Package cards
          _buildPackageCard(
            title: '🏷️ باقة سكاي_هوم 🔥',
            image: 'assets/images/1.jpg',
            features: [
              'حصة شهرية 📥 : 50 قيقا بايت',
              'سرعة التنزيل 📤 : مفتوحة 🚀',
              'سعر الباقة 💵 : 5000 ريال يمني🔥',
              'كل 10 قيقا بـ الف ريال يمني لاغير 😵‍💫🤩',
            ],
            hashtags: [
              '#باقات_سكاي_نت_المنزلية!',
              '#اختار_صح_واختار_سكاي_نت!',
            ],
            gradientColors: AppColors.cardGradient1,
            titleColor: const Color(0xFF77c2ff),
          ),
          
          const SizedBox(height: 20),
          
          _buildPackageCard(
            title: '🏷️ باقة سكاي_برايم 🔥',
            image: 'assets/images/2.jpg',
            features: [
              'حصة شهرية 📥 : 70 قيقا بايت',
              'سرعة التنزيل 📤 : مفتوحة 🚀',
              'سعر الباقة 💵 : 7000 ريال يمني🔥',
              'كل 10 قيقا بـ الف ريال يمني لاغير 😵‍💫🤩',
            ],
            hashtags: [
              '#باقات_سكاي_نت_المنزلية!',
              '#اختار_صح_واختار_سكاي_نت!',
            ],
            gradientColors: AppColors.cardGradient2,
            titleColor: const Color(0xFFdf1324),
          ),
          
          const SizedBox(height: 20),
          
          _buildPackageCard(
            title: '🏷️ باقة سكاي_أكسترا 🔥',
            image: 'assets/images/3.jpg',
            features: [
              'حصة شهرية 📥 : 100 قيقا بايت',
              'سرعة التنزيل 📤 : مفتوحة 🚀',
              'سعر الباقة 💵 : 10000 ريال يمني🔥',
              'كل 10 قيقا بـ الف ريال يمني لاغير 😵‍💫🤩',
            ],
            hashtags: [
              '#باقات_سكاي_نت_المنزلية!',
              '#اختار_صح_واختار_سكاي_نت!',
            ],
            gradientColors: AppColors.cardGradient3,
            titleColor: Colors.white,
          ),
          
          const SizedBox(height: 20),
          
          // Payment info
          _buildPaymentInfo(),
        ],
      ),
    );
  }

  Widget _buildPackageCard({
    required String title,
    required String image,
    required List<String> features,
    required List<String> hashtags,
    required List<Color> gradientColors,
    required Color titleColor,
  }) {
    return Container(
      width: 300,
      margin: const EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowDark,
            offset: Offset(5, 5),
            blurRadius: 5,
          ),
          BoxShadow(
            color: AppColors.shadowLight,
            offset: Offset(-5, -5),
            blurRadius: 5,
          ),
        ],
        color: AppColors.background,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Stack(
          children: [
            // Background image placeholder
            Container(
              height: 250,
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    gradientColors[0].withOpacity(0.3),
                    gradientColors[0].withOpacity(0.7),
                  ],
                ),
              ),
              child: const Icon(
                Icons.image,
                size: 100,
                color: Colors.white54,
              ),
            ),
            
            // Content
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(15),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      gradientColors[0],
                    ],
                    stops: const [0.0, 0.48],
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: titleColor,
                      ),
                    ),
                    
                    const SizedBox(height: 10),
                    
                    // Features
                    ...features.map((feature) => Padding(
                      padding: const EdgeInsets.only(bottom: 5),
                      child: Text(
                        feature,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white,
                          height: 1.6,
                        ),
                      ),
                    )).toList(),
                    
                    const SizedBox(height: 10),
                    
                    // Hashtags
                    Container(
                      padding: const EdgeInsets.only(top: 12),
                      decoration: const BoxDecoration(
                        border: Border(
                          top: BorderSide(color: Colors.white54, width: 1),
                        ),
                      ),
                      child: Wrap(
                        spacing: 8,
                        children: hashtags.map((hashtag) => Text(
                          hashtag,
                          style: TextStyle(
                            fontSize: 14,
                            color: titleColor,
                          ),
                        )).toList(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfo() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 10),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowDark,
            offset: Offset(5, 5),
            blurRadius: 5,
          ),
          BoxShadow(
            color: AppColors.shadowLight,
            offset: Offset(-5, -5),
            blurRadius: 5,
          ),
        ],
        color: AppColors.background,
      ),
      child: Column(
        children: [
          const Text(
            'لإيداع قيمة الأشتراك على حسابنا عمقي',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 10),
          
          Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: AppColors.success,
              borderRadius: BorderRadius.circular(20),
              boxShadow: const [
                BoxShadow(
                  color: AppColors.shadowDark,
                  offset: Offset(5, 5),
                  blurRadius: 5,
                ),
                BoxShadow(
                  color: AppColors.shadowLight,
                  offset: Offset(-5, -5),
                  blurRadius: 5,
                ),
              ],
            ),
            child: const Text(
              '254205081 - رائد خميس مبارك فراره',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
