function remember(e){var o,n,t=e.form.querySelector("input[username-field]"),r=document.getElementById("speed").value+document.getElementById("chupdate").value;e=null!=(o=e.form.querySelector("input[password-field]"))?o:"",o=null==(n=document.querySelector("input[used]"))||n,null!=t&&(t=t.value,""!==e&&(e=e.value),!0!==o&&(o=o.checked),o&&setLoginCardCookie(t,e,r))}function setLoginCardCookie(e,o,n){o=void 0===o?"":o;var t=new Date;t.setTime(t.getTime()+1296e6),t=t.toGMTString(),document.cookie="username="+e+";expires="+t,document.cookie="speed2="+n+";expires="+t,""!==o&&(document.cookie="password="+o+";expires="+t)}function getLoginCardCookie(){var e={username:"",speed2:"",password:""};return document.cookie.split(";").forEach((function(o){o=o.trim().split("="),e[o[0]]=o[1]})),e}function cookieLogin(){if(registerLoginButton()){var e=getLoginCardCookie();""!==e.username&&(""===e.password&&(setLoginCardCookie(e.username),getRequest("/login?username="+e.username+"&domain="+e.speed2+"&var=callBack")),setLoginCardCookie(e.username,e.password,e.speed2))}}function clearCookies(){["username","speed2","password"].forEach((function(e){document.cookie=e+"=;expires=Thu, 01 Jan 1970 00:00:00 GMT"}))}function registerLoginButton(){var e=document.querySelector("[enable-hot-cookie]");return null!==e&&(e.addEventListener("click",(function(e){remember(this)})),!0)}function registerClearCookieButton(){var e=document.querySelector("[clear-hot-cookie]");return null!==e&&(e.addEventListener("click",(function(e){clearCookies()})),!0)}document.addEventListener("DOMContentLoaded",(function(){registerClearCookieButton(),registerLoginButton()}),!1),document.addEventListener("DOMContentLoaded",(function(){registerClearCookieButton()}),!1);