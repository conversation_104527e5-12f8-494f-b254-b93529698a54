class LoginResponse {
  final bool isSuccess;
  final String? error;
  final Map<String, dynamic>? data;

  LoginResponse._({
    required this.isSuccess,
    this.error,
    this.data,
  });

  factory LoginResponse.success(Map<String, dynamic> data) {
    return LoginResponse._(
      isSuccess: true,
      data: data,
    );
  }

  factory LoginResponse.error(String error) {
    return LoginResponse._(
      isSuccess: false,
      error: error,
    );
  }
}

class StatusResponse {
  final bool loggedIn;
  final String? username;
  final String? bytesIn;
  final String? bytesOut;
  final String? remainBytesTotal;
  final String? sessionTimeLeft;
  final String? uptime;
  final String? sps; // Speed and update settings
  final String? subsName;

  StatusResponse({
    required this.loggedIn,
    this.username,
    this.bytesIn,
    this.bytesOut,
    this.remainBytesTotal,
    this.sessionTimeLeft,
    this.uptime,
    this.sps,
    this.subsName,
  });

  factory StatusResponse.fromJson(Map<String, dynamic> json) {
    return StatusResponse(
      loggedIn: json['logged_in'] ?? false,
      username: json['username'],
      bytesIn: json['bytes_in'],
      bytesOut: json['bytes_out'],
      remainBytesTotal: json['remain_bytes_total'],
      sessionTimeLeft: json['session_time_left'],
      uptime: json['uptime'],
      sps: json['sps'],
      subsName: json['SubsName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'logged_in': loggedIn,
      'username': username,
      'bytes_in': bytesIn,
      'bytes_out': bytesOut,
      'remain_bytes_total': remainBytesTotal,
      'session_time_left': sessionTimeLeft,
      'uptime': uptime,
      'sps': sps,
      'SubsName': subsName,
    };
  }
}

class CardInfo {
  final String name;
  final int bytes;

  CardInfo({
    required this.name,
    required this.bytes,
  });
}

// Card information mapping (from JavaScript)
class CardData {
  static final Map<String, CardInfo> cards = {
    '12002': CardInfo(name: '200 ريال', bytes: 838860800),
    '13003': CardInfo(name: '300 ريال', bytes: 1283457024),
    '14004': CardInfo(name: '400 ريال', bytes: 1912602624),
    '15005': CardInfo(name: '500 ريال', bytes: 2671771648),
    '11000': CardInfo(name: '1000 ريال', bytes: 5368709120),
    '11500': CardInfo(name: '1500 ريال', bytes: 8589934592),
    '12000': CardInfo(name: '2000 ريال', bytes: 12884901888),
    '13000': CardInfo(name: '3000 ريال', bytes: 19327352832),
    '15000': CardInfo(name: 'سكاي هوم', bytes: 53687091200),
    '17000': CardInfo(name: 'سكاي برايم', bytes: 75161927680),
    '10000': CardInfo(name: 'سكاي أكسترا', bytes: 107374182400),
    '16060': CardInfo(name: 'باقة خاصة', bytes: 536870912),
  };

  static CardInfo? getCardInfo(String username) {
    // Try last 4 digits
    String cardKey = '1${username.substring(username.length - 4)}';
    if (cards.containsKey(cardKey)) {
      return cards[cardKey];
    }

    // Try first 4 digits
    cardKey = '1${username.substring(0, 4)}';
    if (cards.containsKey(cardKey)) {
      return cards[cardKey];
    }

    // Special case for 10000
    if (username.startsWith('11000') && username.substring(0, 5) == '10000') {
      return cards['10000'];
    }

    return null;
  }
}

class UsageStatistics {
  final double dataUsagePercentage;
  final double downloadPercentage;
  final double uploadPercentage;
  final String downloadData;
  final String uploadData;
  final String remainingData;
  final String packageName;

  UsageStatistics({
    required this.dataUsagePercentage,
    required this.downloadPercentage,
    required this.uploadPercentage,
    required this.downloadData,
    required this.uploadData,
    required this.remainingData,
    required this.packageName,
  });

  factory UsageStatistics.fromStatus(StatusResponse status) {
    final username = status.username ?? '';
    final cardInfo = CardData.getCardInfo(username);
    
    double dataUsagePercentage = 100.0;
    String packageName = 'باقة عادية';
    String remainingData = '🔥🔥غير محدود 🔥🔥';

    if (cardInfo != null) {
      packageName = cardInfo.name;
      final remainBytes = int.tryParse(status.remainBytesTotal ?? '0') ?? 0;
      if (remainBytes > 0 && cardInfo.bytes > 0) {
        dataUsagePercentage = (remainBytes / (cardInfo.bytes / 100)).clamp(0.0, 100.0);
        remainingData = _formatBytes(remainBytes);
      }
    }

    final bytesIn = int.tryParse(status.bytesIn ?? '0') ?? 0;
    final bytesOut = int.tryParse(status.bytesOut ?? '0') ?? 0;
    final totalBytes = bytesIn + bytesOut;

    double downloadPercentage = 0.0;
    double uploadPercentage = 0.0;

    if (totalBytes > 0) {
      downloadPercentage = (bytesOut / (totalBytes / 100)).clamp(0.0, 100.0);
      uploadPercentage = (bytesIn / (totalBytes / 100)).clamp(0.0, 100.0);
    }

    return UsageStatistics(
      dataUsagePercentage: dataUsagePercentage,
      downloadPercentage: downloadPercentage,
      uploadPercentage: uploadPercentage,
      downloadData: _formatBytes(bytesOut),
      uploadData: _formatBytes(bytesIn),
      remainingData: remainingData,
      packageName: packageName,
    );
  }

  static String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes بايت';
    } else if (bytes < 1048576) {
      return '${(bytes / 1024).round()} كيلوبايت';
    } else if (bytes < 1073741824) {
      return '${(bytes / 1048576).round()} ميجابايت';
    } else {
      return '${(bytes / 1073741824).toStringAsFixed(2)} جيجابايت';
    }
  }
}
