import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../utils/app_colors.dart';
import '../widgets/logo_widget.dart';
import '../widgets/custom_button.dart';
import '../widgets/circular_progress_widget.dart';
import '../services/hotspot_service.dart';
import '../models/hotspot_models.dart';
import '../utils/error_messages.dart';

class StatusScreen extends StatefulWidget {
  final String cardNumber;
  final String selectedSpeed;
  final Function(String) onNavigate;
  final VoidCallback onLogout;

  const StatusScreen({
    super.key,
    required this.cardNumber,
    required this.selectedSpeed,
    required this.onNavigate,
    required this.onLogout,
  });

  @override
  State<StatusScreen> createState() => _StatusScreenState();
}

class _StatusScreenState extends State<StatusScreen> {
  StatusResponse? _currentStatus;
  UsageStatistics? _usageStats;
  String currentSpeed = 'سرعة متوسطة';
  String googleUpdates = 'تم تفعيلها';

  @override
  void initState() {
    super.initState();
    currentSpeed = _getSpeedLabel(widget.selectedSpeed);
    _startStatusUpdates();
  }

  @override
  void dispose() {
    HotspotService.instance.dispose();
    super.dispose();
  }

  void _startStatusUpdates() {
    HotspotService.instance.startStatusQuery((status) {
      if (mounted) {
        setState(() {
          _currentStatus = status;
          _usageStats = UsageStatistics.fromStatus(status);

          // Update speed and update settings from status
          if (status.sps != null) {
            final parts = status.sps!.split('_');
            if (parts.isNotEmpty) {
              currentSpeed = SpeedFormatter.toArabicSpeed(parts[0]);
            }
            if (parts.length > 1) {
              googleUpdates = SpeedFormatter.toArabicUpdate(parts[1]);
            }
          }
        });
      }
    });
  }

  String _getSpeedLabel(String speed) {
    switch (speed) {
      case 'economic':
        return 'سرعة اقتصادية';
      case 'normal':
        return 'سرعة متوسطة';
      case 'high':
        return 'سرعة عالية';
      case 'very':
        return 'سرعة مفتوحة';
      case 'gaming':
        return 'سرعة ألعاب أون لاين';
      default:
        return 'سرعة متوسطة';
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: const EdgeInsets.all(15),
        child: Column(
          children: [
            // Main container
            Container(
              width: double.infinity,
              constraints: const BoxConstraints(maxWidth: 500),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(70),
                boxShadow: const [
                  BoxShadow(
                    color: AppColors.shadowDark,
                    offset: Offset(5, 5),
                    blurRadius: 10,
                  ),
                  BoxShadow(
                    color: AppColors.shadowLight,
                    offset: Offset(-5, -5),
                    blurRadius: 10,
                  ),
                ],
                color: AppColors.background,
              ),
              child: Column(
                children: [
                  const SizedBox(height: 20),

                  // Header
                  _buildHeader(),

                  const SizedBox(height: 20),

                  // Live streaming link
                  _buildLiveStreamingLink(),

                  const SizedBox(height: 20),

                  // Status data
                  _buildStatusData(),

                  const SizedBox(height: 20),

                  // Action buttons
                  _buildActionButtons(),

                  const SizedBox(height: 20),

                  // Navigation buttons
                  _buildNavigationButtons(),

                  const SizedBox(height: 20),

                  // WhatsApp group link
                  _buildWhatsAppGroup(),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          const Text(
            'مرحباً بك !',
            style: TextStyle(
              fontSize: 30,
              color: AppColors.textSecondary,
            ),
          ),
          const Text(
            'في سكاي نت',
            style: TextStyle(
              fontSize: 35,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 10),
          // Logo squares
          Container(
            width: 70,
            height: 70,
            child: Wrap(
              spacing: 5,
              runSpacing: 5,
              children: [
                _buildSquare(AppColors.boxRed),
                _buildSquare(AppColors.boxBlue),
                _buildSquare(AppColors.boxYellow),
                _buildSquare(AppColors.boxGreen),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSquare(Color color) {
    return Container(
      width: 30,
      height: 30,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(10),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowDark,
            offset: Offset(5, 5),
            blurRadius: 5,
          ),
          BoxShadow(
            color: AppColors.shadowLight,
            offset: Offset(-5, -5),
            blurRadius: 5,
          ),
        ],
      ),
    );
  }

  Widget _buildLiveStreamingLink() {
    return GestureDetector(
      onTap: () => _openLiveStreaming(),
      child: Container(
        padding: const EdgeInsets.all(10),
        margin: const EdgeInsets.symmetric(horizontal: 20),
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: AppColors.primary),
        ),
        child: const Text(
          'أضغط هنا للدخول للبث المباشر',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildStatusData() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        children: [
          _buildDataRow('أسم المستخدم :', widget.cardNumber),
          _buildDataRow(
              'أسم الباقة :', _usageStats?.packageName ?? 'باقة عادية'),
          _buildDataRowWithProgress(
              'التحميل المتبقي :',
              _usageStats?.remainingData ?? '🔥🔥غير محدود 🔥🔥',
              _usageStats?.dataUsagePercentage ?? 100.0),
          _buildDataRowWithChart(),
          _buildDataRow(
              'الوقت المتبقي :',
              _currentStatus?.sessionTimeLeft != null
                  ? TimeFormatter.toArabicTime(_currentStatus!.sessionTimeLeft!)
                  : '🔥🔥غير محدود 🔥🔥'),
          _buildDataRow(
              'متصل منذ :',
              _currentStatus?.uptime != null
                  ? TimeFormatter.toArabicTime(_currentStatus!.uptime!)
                  : '00:00:00'),
          _buildDataRowWithDropdown(
              'سرعة الكرت :', currentSpeed, _speedOptions()),
          _buildDataRowWithDropdown(
              'تحديثات جوجل :', googleUpdates, _updateOptions()),
        ],
      ),
    );
  }

  Widget _buildDataRow(String label, String value) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 5),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowDark,
            offset: Offset(5, 5),
            blurRadius: 5,
          ),
          BoxShadow(
            color: AppColors.shadowLight,
            offset: Offset(-5, -5),
            blurRadius: 5,
          ),
        ],
        color: AppColors.background,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 14),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataRowWithProgress(
      String label, String value, double percentage) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 5),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowDark,
            offset: Offset(5, 5),
            blurRadius: 5,
          ),
          BoxShadow(
            color: AppColors.shadowLight,
            offset: Offset(-5, -5),
            blurRadius: 5,
          ),
        ],
        color: AppColors.background,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label, style: const TextStyle(fontSize: 14)),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
          CircularProgressWidget(percentage: percentage),
        ],
      ),
    );
  }

  Widget _buildDataRowWithChart() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 5),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowDark,
            offset: Offset(5, 5),
            blurRadius: 5,
          ),
          BoxShadow(
            color: AppColors.shadowLight,
            offset: Offset(-5, -5),
            blurRadius: 5,
          ),
        ],
        color: AppColors.background,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  children: [
                    const Text('التحميل ', style: TextStyle(fontSize: 12)),
                    Text(
                      _usageStats?.downloadData ?? '0 MB',
                      style: const TextStyle(
                        fontSize: 16,
                        color: AppColors.success,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 5),
                Row(
                  children: [
                    const Text('الرفع ', style: TextStyle(fontSize: 12)),
                    Text(
                      _usageStats?.uploadData ?? '0 MB',
                      style: const TextStyle(
                        fontSize: 16,
                        color: AppColors.warning,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Container(
            width: 70,
            height: 70,
            child: Stack(
              children: [
                CircularProgressIndicator(
                  value: (_usageStats?.downloadPercentage ?? 0) / 100,
                  strokeWidth: 10,
                  backgroundColor: AppColors.warning,
                  valueColor:
                      const AlwaysStoppedAnimation<Color>(AppColors.success),
                ),
                const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'D',
                        style: TextStyle(
                          color: AppColors.success,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '/',
                        style: TextStyle(fontSize: 12),
                      ),
                      Text(
                        'U',
                        style: TextStyle(
                          color: AppColors.warning,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataRowWithDropdown(
      String label, String value, List<String> options) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 5),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowDark,
            offset: Offset(5, 5),
            blurRadius: 5,
          ),
          BoxShadow(
            color: AppColors.shadowLight,
            offset: Offset(-5, -5),
            blurRadius: 5,
          ),
        ],
        color: AppColors.background,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label, style: const TextStyle(fontSize: 14)),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () => _showSettingsDialog(label, options),
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                border: Border.all(width: 5),
                boxShadow: const [
                  BoxShadow(
                    color: AppColors.shadowDark,
                    offset: Offset(5, 5),
                    blurRadius: 5,
                  ),
                  BoxShadow(
                    color: AppColors.shadowLight,
                    offset: Offset(-5, -5),
                    blurRadius: 5,
                  ),
                ],
                color: AppColors.background,
              ),
              child: const Icon(
                Icons.settings,
                color: AppColors.primary,
                size: 25,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<String> _speedOptions() {
    return [
      'سرعة اقتصادية',
      'سرعة متوسطة',
      'سرعة عالية',
      'سرعة مفتوحة',
      'سرعة ألعاب أون لاين',
      'إلغاء',
    ];
  }

  List<String> _updateOptions() {
    return [
      'تفعيل التحديثات',
      'أيقاف التحديثات',
      'إلغاء',
    ];
  }

  Widget _buildActionButtons() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: widget.onLogout,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                elevation: 5,
              ),
              child: const Text('تسجيل الخروج'),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: ElevatedButton(
              onPressed: _disconnectInternet,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                elevation: 5,
              ),
              child: const Text('قطع الاتصال'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        CustomButton(
          icon: 'assets/icons/d.svg',
          label: 'أسعار الكروت',
          onPressed: () => widget.onNavigate('prices'),
        ),
        CustomButton(
          icon: 'assets/icons/c.svg',
          label: 'اتصل بنا',
          onPressed: () => _makePhoneCall('+967770160624'),
        ),
        CustomButton(
          icon: 'assets/icons/l.svg',
          label: 'أماكن البيع',
          onPressed: () => widget.onNavigate('locations'),
        ),
      ],
    );
  }

  Widget _buildWhatsAppGroup() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          const Text(
            'الان يمكنكم الانظام لمجموعتنا على واتس اب للاطلاع على اخر العروض و الخدمات',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 14),
          ),
          const SizedBox(height: 10),
          GestureDetector(
            onTap: () => _joinWhatsAppGroup(),
            child: Container(
              width: double.infinity,
              height: 50,
              decoration: BoxDecoration(
                color: AppColors.whatsapp,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.white, width: 2),
                boxShadow: const [
                  BoxShadow(
                    color: AppColors.shadowDark,
                    offset: Offset(5, 5),
                    blurRadius: 5,
                  ),
                  BoxShadow(
                    color: AppColors.shadowLight,
                    offset: Offset(-5, -5),
                    blurRadius: 5,
                  ),
                ],
              ),
              child: const Center(
                child: Text(
                  'أضغط للأنظمام',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _openLiveStreaming() async {
    // In a real app, this would open the live streaming page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('فتح البث المباشر...')),
    );
  }

  void _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);
    await launchUrl(launchUri);
  }

  void _joinWhatsAppGroup() async {
    const String groupUrl = 'https://chat.whatsapp.com/CsnnjJn6OuTGALRtA1LT6Y';
    final Uri launchUri = Uri.parse(groupUrl);
    await launchUrl(launchUri);
  }

  void _showSettingsDialog(String title, List<String> options) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: options
              .map((option) => ListTile(
                    title: Text(option),
                    onTap: () {
                      Navigator.pop(context);
                      _handleSettingChange(title, option);
                    },
                  ))
              .toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _handleSettingChange(String settingType, String newValue) async {
    if (newValue == 'إلغاء') return;

    bool success = false;

    if (settingType.contains('سرعة')) {
      // Change speed
      String speedKey = _getSpeedKey(newValue);
      success = await HotspotService.instance.changeSpeed(speedKey);
    } else if (settingType.contains('تحديثات')) {
      // Change update settings
      String updateKey = newValue.contains('أيقاف') ? '_Uoff' : '_Uon';
      success = await HotspotService.instance.changeUpdateSettings(updateKey);
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              success ? 'تم تغيير الإعدادات بنجاح' : 'فشل في تغيير الإعدادات'),
          backgroundColor: success ? AppColors.success : AppColors.error,
        ),
      );
    }
  }

  String _getSpeedKey(String speedLabel) {
    switch (speedLabel) {
      case 'سرعة اقتصادية':
        return 'economic';
      case 'سرعة متوسطة':
        return 'normal';
      case 'سرعة عالية':
        return 'high';
      case 'سرعة مفتوحة':
        return 'very';
      case 'سرعة ألعاب أون لاين':
        return 'gaming';
      default:
        return 'normal';
    }
  }

  void _disconnectInternet() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('قطع الاتصال'),
        content: const Text('هل تريد قطع الاتصال بالإنترنت؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('قطع الاتصال'),
          ),
        ],
      ),
    );

    if (result == true) {
      final success = await HotspotService.instance.logout();
      if (mounted) {
        if (success) {
          widget.onLogout();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في قطع الاتصال'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }
}
