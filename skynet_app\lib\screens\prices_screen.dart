import 'package:flutter/material.dart';
import '../utils/app_colors.dart';
import '../widgets/custom_button.dart';

class PricesScreen extends StatelessWidget {
  final VoidCallback onBack;

  const PricesScreen({super.key, required this.onBack});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: const EdgeInsets.all(15),
        child: Column(
          children: [
            // Main container
            Container(
              width: double.infinity,
              constraints: const BoxConstraints(maxWidth: 500),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(70),
                boxShadow: const [
                  BoxShadow(
                    color: AppColors.shadowDark,
                    offset: Offset(5, 5),
                    blurRadius: 10,
                  ),
                  BoxShadow(
                    color: AppColors.shadowLight,
                    offset: Offset(-5, -5),
                    blurRadius: 10,
                  ),
                ],
                color: AppColors.background,
              ),
              child: <PERSON>umn(
                children: [
                  const SizedBox(height: 20),
                  
                  // Header
                  _buildHeader(),
                  
                  const SizedBox(height: 20),
                  
                  // Prices content
                  _buildPricesContent(),
                  
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          // Text section
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const Text(
                  'أسعار !',
                  style: TextStyle(
                    fontSize: 30,
                    color: AppColors.textSecondary,
                  ),
                ),
                const Text(
                  'كروت الإنترنت 🛒',
                  style: TextStyle(
                    fontSize: 35,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 20),
          
          // Back button
          GestureDetector(
            onTap: onBack,
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                border: Border.all(color: Colors.black),
                boxShadow: const [
                  BoxShadow(
                    color: AppColors.shadowDark,
                    offset: Offset(5, 5),
                    blurRadius: 5,
                  ),
                  BoxShadow(
                    color: AppColors.shadowLight,
                    offset: Offset(-5, -5),
                    blurRadius: 5,
                  ),
                ],
                color: AppColors.background,
              ),
              child: const Icon(
                Icons.arrow_back,
                size: 30,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPricesContent() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 10),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(30),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowDark,
            offset: Offset(5, 5),
            blurRadius: 5,
          ),
          BoxShadow(
            color: AppColors.shadowLight,
            offset: Offset(-5, -5),
            blurRadius: 5,
          ),
        ],
        color: AppColors.background,
      ),
      child: Column(
        children: [
          const Text(
            'قائمة الأسعار الحالية',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Prices table
          _buildPricesTable(),
          
          const SizedBox(height: 20),
          
          // Home packages button
          SizedBox(
            width: 250,
            height: 50,
            child: ElevatedButton(
              onPressed: () {
                // Navigate to home packages
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                elevation: 5,
              ),
              child: const Text(
                'تصفح الباقات المنزلية 🏠',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPricesTable() {
    final List<Map<String, String>> pricesData = [
      {
        'price': '200 ريال',
        'time': 'مفتوح',
        'data': '800 ميجا',
        'validity': '2 يوم',
      },
      {
        'price': '300 ريال',
        'time': 'مفتوح',
        'data': '1200 ميجا',
        'validity': '5 أيام',
      },
      {
        'price': '400 ريال',
        'time': 'مفتوح',
        'data': '1800 ميجا',
        'validity': '7 أيام',
      },
      {
        'price': '500 ريال',
        'time': 'مفتوح',
        'data': '2500 ميجا',
        'validity': '10 أيام',
      },
      {
        'price': '1000 ريال',
        'time': 'مفتوح',
        'data': '5000 ميجا',
        'validity': '30 يوم',
      },
      {
        'price': '1500 ريال',
        'time': 'مفتوح',
        'data': '8000 ميجا',
        'validity': '30 يوم',
      },
      {
        'price': '2000 ريال',
        'time': 'مفتوح',
        'data': '12000 ميجا',
        'validity': '30 يوم',
      },
      {
        'price': '3000 ريال',
        'time': 'مفتوح',
        'data': '20000 ميجا',
        'validity': '30 يوم',
      },
    ];

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppColors.textSecondary, width: 1),
      ),
      child: Column(
        children: [
          // Table header
          Container(
            padding: const EdgeInsets.all(10),
            decoration: const BoxDecoration(
              color: AppColors.secondary,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10),
                topRight: Radius.circular(10),
              ),
            ),
            child: const Row(
              children: [
                Expanded(
                  child: Text(
                    'الفئة',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  child: Text(
                    'الوقت',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  child: Text(
                    'الرصيد',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  child: Text(
                    'الصلاحية',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          
          // Table rows
          ...pricesData.asMap().entries.map((entry) {
            final index = entry.key;
            final data = entry.value;
            final isEven = index % 2 == 0;
            
            return Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: isEven ? Colors.white : AppColors.background,
                border: const Border(
                  bottom: BorderSide(color: AppColors.textSecondary, width: 0.5),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      data['price']!,
                      style: const TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w700,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      data['time']!,
                      style: const TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w700,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      data['data']!,
                      style: const TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w700,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      data['validity']!,
                      style: const TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w700,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }
}
