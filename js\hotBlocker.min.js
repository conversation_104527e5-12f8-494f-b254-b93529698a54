var $jscomp=$jscomp||{};$jscomp.scope={},$jscomp.createTemplateTagFirstArg=function(e){return e.raw=e},$jscomp.createTemplateTagFirstArgWithRaw=function(e,t){return e.raw=t,e};var BLOCKED_TIME="hot_blocker_blocked_time",BLOCKED_FORE="hot_blocker_block_minutes",FAIL_COUNTER="hot_blocker_counter",$block=document.getElementById("block");function incrementCounter(e){var t,o;return e=null!=(t=e.getAttribute("block-time"))?t:5,t=null!=(o=getCookieOrNull(FAIL_COUNTER))?o:0,t=parseInt(t)+1,setCookie(FAIL_COUNTER,t,e),t}function checkFailsCount(e,t){var o,n;t<(null!=(o=e.getAttribute("try-count"))?o:5)||(o=null!=(n=e.getAttribute("block-time"))?n:5,n=timeStamp(),setCookie(BLOCKED_TIME,n,o),setCookie(BLOCKED_FORE,o,o),redirectToBlockPage())}function resetCounter(){setCookie(BLOCKED_TIME,"",0),setCookie(FAIL_COUNTER,"",0)}function redirectToBlockPage(){null!=$block&&(showParentElement($loginSubmit,$block),initCountDown(document.querySelector("span[count-down-span]")))}function checkIsBlocked(){return null!=getCookieOrNull(BLOCKED_TIME)&&(redirectToBlockPage(),!0)}function setCookie(e,t,o){var n=new Date;n.setTime(n.getTime()+6e4*o),o="expires="+n.toUTCString(),document.cookie=e+"="+t+";"+o+";path=/"}function getCookieOrNull(e){var t=null;return document.cookie.split(";").forEach((function(o){if((o=o.trim().split("="))[0]===e)return t=o[1],!0})),t}function timeStamp(){return Math.floor((new Date).getTime())}function initCountDown(e){var t=getCookieOrNull(BLOCKED_TIME);if(null===t)showLoginPage(),hideBlockPage();else{var o=6e4*getCookieOrNull(BLOCKED_FORE)+parseInt(t);(t=function(){var t=timeStamp();t=o-t,e.innerHTML=Math.floor(t%36e5/6e4)+" دقائق و "+Math.floor(t%6e4/1e3)+" ثانية",0>t&&(clearInterval(n),showLoginPage(),hideBlockPage())})();var n=setInterval(t,1e3)}}function hideBlockPage(){null!=$block&&($block.style.display="none",$block.classList.add("inactive"))}document.addEventListener("DOMContentLoaded",(function(){checkIsBlocked()}),!1);