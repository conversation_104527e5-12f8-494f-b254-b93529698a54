import 'package:flutter/material.dart';

class AppColors {
  // Primary colors from the website
  static const Color primary = Color(0xFF56AEEE);
  static const Color secondary = Color(0xFF686868);
  static const Color background = Color(0xFFE1E1E1);
  
  // Gradient colors
  static const Color gradientStart = Color(0xFFfb6777);
  static const Color gradientMiddle = Color(0xFFc9849b);
  static const Color gradientEnd = Color(0xFF61a7e1);
  
  // Box colors (from the squares)
  static const Color boxRed = Color(0xFFff6b6b);
  static const Color boxBlue = Color(0xFF56aeee);
  static const Color boxYellow = Color(0xFFffd167);
  static const Color boxGreen = Color(0xFFa1d9b4);
  
  // Text colors
  static const Color textPrimary = Color(0xFF000000);
  static const Color textSecondary = Color(0xFF686868);
  static const Color textLight = Color(0xFF585858);
  
  // Status colors
  static const Color success = Color(0xFF00c040);
  static const Color error = Color(0xFFdc3747);
  static const Color warning = Color(0xFFc60077);
  
  // Shadow colors
  static const Color shadowLight = Color(0xFFf4f4f4);
  static const Color shadowDark = Color(0xFF9e9e9e);
  
  // WhatsApp green
  static const Color whatsapp = Color(0xFF25d366);
  
  // Weather colors
  static const Color weatherBg = Color(0xFFcdcdcd);
  
  // Card gradients
  static const List<Color> cardGradient1 = [
    Color(0xFF03194a),
    Colors.transparent,
  ];
  
  static const List<Color> cardGradient2 = [
    Color(0xFF2a2a2a),
    Colors.transparent,
  ];
  
  static const List<Color> cardGradient3 = [
    Color(0xFF181f27),
    Colors.transparent,
  ];
  
  // Animated gradient colors
  static const List<Color> animatedGradient = [
    Color(0xFFf79533),
    Color(0xFFf37055),
    Color(0xFFef4e7b),
    Color(0xFFa166ab),
    Color(0xFF5073b8),
    Color(0xFF1098ad),
    Color(0xFF07b39b),
    Color(0xFF6fba82),
  ];
}
