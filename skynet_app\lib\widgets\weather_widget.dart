import 'package:flutter/material.dart';
import '../utils/app_colors.dart';

class WeatherWidget extends StatefulWidget {
  const WeatherWidget({super.key});

  @override
  State<WeatherWidget> createState() => _WeatherWidgetState();
}

class _WeatherWidgetState extends State<WeatherWidget> {
  String temperature = '29°C';
  String condition = 'غائم جزئيا';
  String weatherIcon = 'assets/icons/c.svg';

  @override
  void initState() {
    super.initState();
    _loadWeatherData();
  }

  void _loadWeatherData() {
    // In a real app, this would fetch weather data from an API
    // For now, we'll use static data similar to the website
    setState(() {
      temperature = '29°C';
      condition = 'غائم جزئيا';
      weatherIcon = 'assets/icons/c.svg';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 10),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(40),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowDark,
            offset: Offset(2, 2),
            blurRadius: 3,
          ),
        ],
        color: AppColors.background,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Weather text
          const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'الطقس اليوم :',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          // Weather info
          Row(
            children: [
              // Weather icon
              Container(
                width: 50,
                height: 50,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('assets/icons/c.svg'),
                    fit: BoxFit.contain,
                  ),
                ),
                child: const Icon(
                  Icons.cloud,
                  size: 40,
                  color: AppColors.primary,
                ),
              ),

              const SizedBox(width: 10),

              // Temperature and condition
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    temperature,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    condition,
                    style: const TextStyle(
                      fontSize: 11,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
