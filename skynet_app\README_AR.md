# تطبيق سكاي نت

تطبيق Flutter لشركة سكاي نت لخدمات الإنترنت في اليمن، مطور بناءً على الموقع الأصلي مع نفس الخصائص والوظائف.

## الخصائص الرئيسية

### 🔐 شاشة تسجيل الدخول
- إدخال رقم الكرت
- اختيار سرعة الإنترنت (اقتصادية، متوسطة، عالية، مفتوحة، ألعاب)
- عرض الطقس الحالي
- أزرار التواصل (اتصال، واتساب، معلومات)
- إعدادات تحديثات جوجل

### 📊 شاشة حالة الاتصال
- عرض معلومات المستخدم
- إحصائيات الاستخدام (التحميل والرفع)
- الرسوم البيانية الدائرية للبيانات
- الوقت المتبقي ومدة الاتصال
- إعدادات السرعة والتحديثات
- أزرار قطع الاتصال وتسجيل الخروج

### 💰 شاشة الأسعار
- جدول أسعار كروت الإنترنت
- تفاصيل الفئات والصلاحية
- زر الانتقال للباقات المنزلية

### 🏠 شاشة الباقات المنزلية
- عرض الباقات بالصور والتفاصيل
- باقة سكاي_هوم (50 جيجا - 5000 ريال)
- باقة سكاي_برايم (70 جيجا - 7000 ريال)
- باقة سكاي_أكسترا (100 جيجا - 10000 ريال)
- معلومات الدفع والحساب البنكي

### 📍 شاشة أماكن البيع
- قائمة نقاط البيع مقسمة حسب المناطق
- تفاصيل المحلات والمواقع
- تصميم منظم وسهل القراءة

### ℹ️ شاشة المعلومات
- نبذة عن الشركة وخدماتها
- معلومات الاتصال
- تصميم جذاب مع الأيقونات

## التقنيات المستخدمة

- **Flutter**: إطار العمل الأساسي
- **Dart**: لغة البرمجة
- **Material Design**: نظام التصميم
- **Custom Widgets**: ويدجت مخصصة للتصميم

## المكتبات المستخدمة

```yaml
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  http: ^1.1.0                    # طلبات HTTP
  url_launcher: ^6.2.1           # فتح الروابط والاتصال
  qr_code_scanner: ^1.0.1        # مسح QR Code
  fl_chart: ^0.65.0              # الرسوم البيانية
  shared_preferences: ^2.2.2     # التخزين المحلي
  weather: ^3.1.1                # بيانات الطقس
  font_awesome_flutter: ^10.6.0  # الأيقونات
```

## هيكل المشروع

```
lib/
├── main.dart                    # نقطة البداية
├── screens/                    # الشاشات
│   ├── login_screen.dart       # شاشة تسجيل الدخول
│   ├── home_screen.dart        # الشاشة الرئيسية
│   ├── status_screen.dart      # شاشة حالة الاتصال
│   ├── prices_screen.dart      # شاشة الأسعار
│   ├── packages_screen.dart    # شاشة الباقات المنزلية
│   ├── locations_screen.dart   # شاشة أماكن البيع
│   └── info_screen.dart        # شاشة المعلومات
├── widgets/                    # الويدجت المخصصة
│   ├── logo_widget.dart        # ويدجت الشعار
│   ├── weather_widget.dart     # ويدجت الطقس
│   ├── custom_button.dart      # الأزرار المخصصة
│   ├── custom_dropdown.dart    # القائمة المنسدلة
│   ├── custom_text_field.dart  # حقول النص
│   └── circular_progress_widget.dart # الرسم البياني الدائري
└── utils/
    └── app_colors.dart         # ألوان التطبيق
```

## الألوان والتصميم

التطبيق يستخدم نفس الألوان والتصميم من الموقع الأصلي:

- **اللون الأساسي**: `#56AEEE` (أزرق سكاي نت)
- **اللون الثانوي**: `#686868` (رمادي)
- **الخلفية**: `#E1E1E1` (رمادي فاتح)
- **ألوان المربعات**: أحمر، أزرق، أصفر، أخضر
- **تدرجات متحركة** للنصوص

## كيفية التشغيل

### المتطلبات
- Flutter SDK
- Dart SDK
- محرر نصوص (VS Code, Android Studio)

### خطوات التشغيل

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd skynet_app
```

2. **تحميل التبعيات**
```bash
flutter pub get
```

3. **تشغيل التطبيق**
```bash
# للويب
flutter run -d chrome

# للأندرويد
flutter run -d android

# للويندوز
flutter run -d windows
```

## البناء للإنتاج

### بناء تطبيق الويب
```bash
flutter build web
```

### بناء تطبيق الأندرويد
```bash
flutter build apk --release
```

### بناء تطبيق الويندوز
```bash
flutter build windows --release
```

## الميزات المطبقة

✅ **تسجيل الدخول**: إدخال رقم الكرت واختيار السرعة  
✅ **عرض الطقس**: معلومات الطقس الحالي  
✅ **أزرار التواصل**: اتصال، واتساب، معلومات  
✅ **جدول الأسعار**: عرض أسعار الكروت  
✅ **الباقات المنزلية**: عرض الباقات بالتفاصيل  
✅ **أماكن البيع**: قائمة نقاط البيع  
✅ **حالة الاتصال**: إحصائيات الاستخدام  
✅ **الرسوم البيانية**: عرض البيانات بصرياً  
✅ **التصميم المتجاوب**: يعمل على جميع الأحجام  
✅ **الألوان والتصميم**: مطابق للموقع الأصلي  

## المطور

تم تطوير هذا التطبيق بواسطة Augment Agent باستخدام Flutter، مع الحفاظ على نفس تصميم ووظائف الموقع الأصلي لشركة سكاي نت.

## الترخيص

هذا المشروع مطور لأغراض تعليمية وتجريبية. جميع الحقوق محفوظة لشركة سكاي نت.
