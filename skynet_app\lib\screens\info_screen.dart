import 'package:flutter/material.dart';
import '../utils/app_colors.dart';

class InfoScreen extends StatelessWidget {
  final VoidCallback onBack;

  const InfoScreen({super.key, required this.onBack});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: const EdgeInsets.all(15),
        child: Column(
          children: [
            // Main container
            Container(
              width: double.infinity,
              constraints: const BoxConstraints(maxWidth: 500),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(70),
                boxShadow: const [
                  BoxShadow(
                    color: AppColors.shadowDark,
                    offset: Offset(5, 5),
                    blurRadius: 10,
                  ),
                  BoxShadow(
                    color: AppColors.shadowLight,
                    offset: Offset(-5, -5),
                    blurRadius: 10,
                  ),
                ],
                color: AppColors.background,
              ),
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  
                  // Header
                  _buildHeader(),
                  
                  const SizedBox(height: 20),
                  
                  // Info content
                  _buildInfoContent(),
                  
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          // Text section
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const Text(
                  'عن',
                  style: TextStyle(
                    fontSize: 30,
                    color: AppColors.textSecondary,
                  ),
                ),
                const Text(
                  'سكاي نت',
                  style: TextStyle(
                    fontSize: 35,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 20),
          
          // Logo and back button
          Stack(
            children: [
              // Back button
              GestureDetector(
                onTap: onBack,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(color: Colors.black),
                    boxShadow: const [
                      BoxShadow(
                        color: AppColors.shadowDark,
                        offset: Offset(5, 5),
                        blurRadius: 5,
                      ),
                      BoxShadow(
                        color: AppColors.shadowLight,
                        offset: Offset(-5, -5),
                        blurRadius: 5,
                      ),
                    ],
                    color: AppColors.background,
                  ),
                  child: const Icon(
                    Icons.arrow_back,
                    size: 30,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              
              // Logo
              Positioned(
                right: 60,
                top: 5,
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(25),
                    color: AppColors.primary,
                  ),
                  child: const Icon(
                    Icons.wifi,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoContent() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 10),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(30),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowDark,
            offset: Offset(5, 5),
            blurRadius: 5,
          ),
          BoxShadow(
            color: AppColors.shadowLight,
            offset: Offset(-5, -5),
            blurRadius: 5,
          ),
        ],
        color: AppColors.background,
      ),
      child: Column(
        children: [
          const Text(
            'نبذة عن خدمتنا',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          
          const SizedBox(height: 20),
          
          _buildInfoCard(
            icon: Icons.wifi,
            title: 'خدمة إنترنت متميزة',
            description: 'نحن نسعى لتقديم أفضل خدمة إنترنت لعملائنا الكرام.',
          ),
          
          const SizedBox(height: 15),
          
          _buildInfoCard(
            icon: Icons.price_check,
            title: 'أسعار تنافسية',
            description: 'بجودة عالية وأسعار تنافسية تناسب جميع الفئات.',
          ),
          
          const SizedBox(height: 15),
          
          _buildInfoCard(
            icon: Icons.speed,
            title: 'تصفح سريع',
            description: 'استمتع بتصفح سريع، ومشاهدة ممتعة، وتواصل غير محدود.',
          ),
          
          const SizedBox(height: 15),
          
          _buildInfoCard(
            icon: Icons.support_agent,
            title: 'دعم فني متميز',
            description: 'هدفنا هو راحتك وتوفير تجربة إنترنت مميزة في بيئة مريحة ومرحبة.',
          ),
          
          const SizedBox(height: 20),
          
          // Contact information
          _buildContactInfo(),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: Colors.white,
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowDark,
            offset: Offset(2, 2),
            blurRadius: 3,
          ),
        ],
      ),
      child: Row(
        children: [
          // Icon
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(25),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 25,
            ),
          ),
          
          const SizedBox(width: 15),
          
          // Text content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 5),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        gradient: LinearGradient(
          colors: [
            AppColors.primary.withOpacity(0.1),
            AppColors.primary.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: Border.all(color: AppColors.primary.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Text(
            'تواصل معنا',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          
          const SizedBox(height: 15),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildContactItem(
                icon: Icons.phone,
                label: 'اتصل بنا',
                value: '+967770160624',
              ),
              _buildContactItem(
                icon: Icons.chat,
                label: 'واتساب',
                value: '+967770160624',
              ),
            ],
          ),
          
          const SizedBox(height: 15),
          
          const Text(
            'نحن هنا لخدمتكم على مدار الساعة',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildContactItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            icon,
            color: Colors.white,
            size: 20,
          ),
        ),
        const SizedBox(height: 5),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 10,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
}
