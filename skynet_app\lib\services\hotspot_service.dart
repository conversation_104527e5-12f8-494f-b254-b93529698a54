import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/hotspot_models.dart';
import '../utils/error_messages.dart';

class HotspotService {
  static const String baseUrl = 'http://10.10.10.1';
  static HotspotService? _instance;
  
  // Singleton pattern
  static HotspotService get instance {
    _instance ??= HotspotService._internal();
    return _instance!;
  }
  
  HotspotService._internal();
  
  Timer? _statusTimer;
  bool _isLoggedIn = false;
  String _currentUsername = '';
  
  // Login function
  Future<LoginResponse> login({
    required String username,
    required String domain,
    String password = '',
    String updateSettings = '',
  }) async {
    try {
      final url = '$baseUrl/login?username=$username&password=$password&domain=$domain$updateSettings&var=callBack';
      
      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode >= 200 && response.statusCode < 400) {
        final data = json.decode(response.body);
        
        if (data['action'] == 'onLoggedIn') {
          _isLoggedIn = true;
          _currentUsername = username;
          return LoginResponse.success(data);
        } else if (data['action'] == 'onLoginError') {
          return LoginResponse.error(ErrorMessages.toArabic(data['error'] ?? 'Unknown error'));
        }
      }
      
      return LoginResponse.error('فشل في الاتصال بالسيرفر');
    } catch (e) {
      return LoginResponse.error('خطأ في الشبكة: ${e.toString()}');
    }
  }
  
  // Logout function
  Future<bool> logout({bool eraseCookie = false}) async {
    try {
      final url = eraseCookie 
          ? '$baseUrl/logout?erase-cookie=yes&var=callBack'
          : '$baseUrl/logout?var=callBack';
      
      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode >= 200 && response.statusCode < 400) {
        _isLoggedIn = false;
        _currentUsername = '';
        _stopStatusQuery();
        return true;
      }
      
      return false;
    } catch (e) {
      return false;
    }
  }
  
  // Status query function
  Future<StatusResponse?> getStatus() async {
    if (!_isLoggedIn) return null;
    
    try {
      final url = '$baseUrl/status?var=callBack';
      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode >= 200 && response.statusCode < 400) {
        final data = json.decode(response.body);
        
        if (data['logged_in'] == true) {
          return StatusResponse.fromJson(data);
        } else {
          _isLoggedIn = false;
          _stopStatusQuery();
          return null;
        }
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }
  
  // Start periodic status queries
  void startStatusQuery(Function(StatusResponse) onStatusUpdate) {
    if (_statusTimer != null) return;
    
    _statusTimer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (!_isLoggedIn) {
        timer.cancel();
        return;
      }
      
      final status = await getStatus();
      if (status != null) {
        onStatusUpdate(status);
      }
    });
  }
  
  // Stop status queries
  void _stopStatusQuery() {
    _statusTimer?.cancel();
    _statusTimer = null;
  }
  
  // Change speed
  Future<bool> changeSpeed(String newSpeed) async {
    if (!_isLoggedIn || _currentUsername.isEmpty) return false;
    
    try {
      // First logout
      await logout();
      
      // Wait a bit
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Login with new speed
      final result = await login(
        username: _currentUsername,
        domain: newSpeed,
      );
      
      return result.isSuccess;
    } catch (e) {
      return false;
    }
  }
  
  // Change update settings
  Future<bool> changeUpdateSettings(String updateSetting) async {
    if (!_isLoggedIn || _currentUsername.isEmpty) return false;
    
    try {
      // Get current speed from status
      final status = await getStatus();
      if (status == null) return false;
      
      final currentSpeed = status.sps?.split('_')[0] ?? 'normal';
      
      // First logout
      await logout();
      
      // Wait a bit
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Login with new update setting
      final result = await login(
        username: _currentUsername,
        domain: currentSpeed + updateSetting,
      );
      
      return result.isSuccess;
    } catch (e) {
      return false;
    }
  }
  
  // Check if logged in
  bool get isLoggedIn => _isLoggedIn;
  
  // Get current username
  String get currentUsername => _currentUsername;
  
  // Dispose resources
  void dispose() {
    _stopStatusQuery();
  }
}
