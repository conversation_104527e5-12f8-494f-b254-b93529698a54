import 'package:flutter/material.dart';
import '../utils/app_colors.dart';

class LocationsScreen extends StatelessWidget {
  final VoidCallback onBack;

  const LocationsScreen({super.key, required this.onBack});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: const EdgeInsets.all(15),
        child: Column(
          children: [
            // Main container
            Container(
              width: double.infinity,
              constraints: const BoxConstraints(maxWidth: 500),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(70),
                boxShadow: const [
                  BoxShadow(
                    color: AppColors.shadowDark,
                    offset: Offset(5, 5),
                    blurRadius: 10,
                  ),
                  BoxShadow(
                    color: AppColors.shadowLight,
                    offset: Offset(-5, -5),
                    blurRadius: 10,
                  ),
                ],
                color: AppColors.background,
              ),
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  
                  // Header
                  _buildHeader(),
                  
                  const SizedBox(height: 20),
                  
                  // Locations content
                  _buildLocationsContent(),
                  
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          // Text section
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const Text(
                  'أماكن !',
                  style: TextStyle(
                    fontSize: 30,
                    color: AppColors.textSecondary,
                  ),
                ),
                const Text(
                  'بيع الكروت 🏠',
                  style: TextStyle(
                    fontSize: 35,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 20),
          
          // Back button
          GestureDetector(
            onTap: onBack,
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                border: Border.all(color: Colors.black),
                boxShadow: const [
                  BoxShadow(
                    color: AppColors.shadowDark,
                    offset: Offset(5, 5),
                    blurRadius: 5,
                  ),
                  BoxShadow(
                    color: AppColors.shadowLight,
                    offset: Offset(-5, -5),
                    blurRadius: 5,
                  ),
                ],
                color: AppColors.background,
              ),
              child: const Icon(
                Icons.arrow_back,
                size: 30,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationsContent() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 10),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(30),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowDark,
            offset: Offset(5, 5),
            blurRadius: 5,
          ),
          BoxShadow(
            color: AppColors.shadowLight,
            offset: Offset(-5, -5),
            blurRadius: 5,
          ),
        ],
        color: AppColors.background,
      ),
      child: Column(
        children: [
          // Scrollable locations list
          Container(
            height: 400,
            child: SingleChildScrollView(
              child: _buildLocationsList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationsList() {
    final List<Map<String, dynamic>> locations = [
      {
        'area': 'مسجد النور',
        'shops': ['1- بقالة النور بايزيد'],
      },
      {
        'area': 'مسجد التوحيد',
        'shops': ['1- بقالة التوحيد'],
      },
      {
        'area': 'مسجد أبوبكر',
        'shops': [
          '1- مقهى السلام لاند',
          '2- بقالة باسعيدة',
          '3- بقالة الجود',
        ],
      },
      {
        'area': 'مسجد إبراهيم',
        'shops': [
          '1- بقالة وتموينات بازهير',
          '2- بقالة فرج عبيد',
          '3- مقصف مدرسة شحوح',
        ],
      },
      {
        'area': 'مسجد ابن باز',
        'shops': [
          '1- بقالة بن زيدان',
          '2- بقالة الجريدي',
        ],
      },
      {
        'area': 'نادي الأتحاد',
        'shops': [
          '1- صالون الأتحاد',
          '2- خضار وفواكه الخير',
        ],
      },
      {
        'area': 'نقاط البيع في السحيل',
        'shops': [
          '1- بقالة التوفير',
          '2- بقالة بلال',
          '3- بقالة السدة',
        ],
      },
      {
        'area': 'شارع الورش',
        'shops': [
          '1- شارع الورش محلات الجودة بالرقعان',
          '2- مسبح بازمول عيديد',
        ],
      },
    ];

    return Column(
      children: locations.map((location) => _buildLocationCard(
        location['area'] as String,
        location['shops'] as List<String>,
      )).toList(),
    );
  }

  Widget _buildLocationCard(String area, List<String> shops) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 15),
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: AppColors.textSecondary, width: 1),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Area name
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              area,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          const SizedBox(height: 10),
          
          // Shops list
          ...shops.map((shop) => Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 10),
            margin: const EdgeInsets.only(bottom: 5),
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.primary.withOpacity(0.3)),
            ),
            child: Text(
              shop,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary,
              ),
            ),
          )).toList(),
        ],
      ),
    );
  }
}
