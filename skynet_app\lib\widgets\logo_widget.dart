import 'package:flutter/material.dart';
import '../utils/app_colors.dart';

class LogoWidget extends StatefulWidget {
  const LogoWidget({super.key});

  @override
  State<LogoWidget> createState() => _LogoWidgetState();
}

class _LogoWidgetState extends State<LogoWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_animationController);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          // Text section
          Expanded(
            child: <PERSON>umn(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const Text(
                  'شبكة !',
                  style: TextStyle(
                    fontSize: 30,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.normal,
                  ),
                  textAlign: TextAlign.right,
                ),
                AnimatedBuilder(
                  animation: _animation,
                  builder: (context, child) {
                    return ShaderMask(
                      shaderCallback: (bounds) {
                        return LinearGradient(
                          colors: AppColors.animatedGradient,
                          stops: const [0.0, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.4],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          transform: GradientRotation(_animation.value * 2 * 3.14159),
                        ).createShader(bounds);
                      },
                      child: const Text(
                        'سـكــاي نــت',
                        style: TextStyle(
                          fontSize: 45,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 20),
          
          // Logo squares
          Container(
            width: 70,
            height: 70,
            child: Wrap(
              spacing: 5,
              runSpacing: 5,
              children: [
                _buildSquare(AppColors.boxRed),
                _buildSquare(AppColors.boxBlue),
                _buildSquare(AppColors.boxYellow),
                _buildSquare(AppColors.boxGreen),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSquare(Color color) {
    return Container(
      width: 30,
      height: 30,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(10),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowDark,
            offset: Offset(5, 5),
            blurRadius: 5,
          ),
          BoxShadow(
            color: AppColors.shadowLight,
            offset: Offset(-5, -5),
            blurRadius: 5,
          ),
        ],
      ),
    );
  }
}
